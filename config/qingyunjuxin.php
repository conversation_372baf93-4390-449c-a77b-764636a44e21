<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 青云聚信配送平台配置
    |--------------------------------------------------------------------------
    |
    | 这里配置青云聚信配送平台相关的参数
    |
    */

    // 青云聚信回调地址
    'callback_url' => env('QINGYUNJUXIN_CALLBACK_URL', ''),

    // 青云聚信调用协议配置
    'app_key' => env('QINGYUNJUXIN_APP_KEY', 'QYJ_APP_KEY'),
    'app_secret' => env('QINGYUNJUXIN_APP_SECRET', 'QYJ_APP_SECRET'),

    // 青云聚信环境配置
    'environment' => env('QINGYUNJUXIN_ENVIRONMENT', 'test'), // test, production
    'base_urls' => [
        'test' => env('QINGYUNJUXIN_TEST_URL', 'https://test-api.qingyunjuxin.com'),
        'production' => env('QINGYUNJUXIN_PRODUCTION_URL', 'https://api.qingyunjuxin.com'),
    ],

    // 状态映射配置（根据青云聚信实际状态定义）
    'status_mapping' => [
        'created' => 10,        // 已创建(待接单)
        'accepted' => 20,       // 骑手已接单
        'picked_up' => 30,      // 已取货
        'delivered' => 50,      // 已送达
        'cancelled' => 99,      // 已取消
    ],

    // 取消原因映射
    'cancel_reason_mapping' => [
        1 => "商户取消",
        2 => "用户取消",
        3 => "系统取消",
        4 => "骑手取消",
        5 => "配送商取消",
        99 => "其他原因",
    ],

    // 商品分类映射（青云聚信分类到系统分类）
    'goods_category_mapping' => [
        100 => 5,   // 美食 -> 餐饮美食
        104 => 7,   // 生鲜果蔬 -> 生鲜果蔬
        108 => 14,  // 医药健康 -> 医药健康
        105 => 14,  // 超市百货 -> 超市百货
        103 => 9,   // 鲜花绿植 -> 鲜花绿植
        102 => 8,   // 烘焙蛋糕 -> 烘焙蛋糕
        101 => 5,   // 饮品奶茶 -> 饮品奶茶
        999 => 14,  // 其他 -> 其他
    ],

    // 订单来源映射
    'order_source_mapping' => [
        100 => '青云聚信｜美团',
        200 => '青云聚信｜饿了么',
        300 => '青云聚信｜京东',
        400 => '青云聚信自营',
        500 => '青云聚信｜其他',
    ],

    // API超时配置
    'timeout' => [
        'connect' => 10, // 连接超时时间（秒）
        'read' => 30,    // 读取超时时间（秒）
    ],

    // 重试配置
    'retry' => [
        'max_attempts' => 3,  // 最大重试次数
        'delay' => 1000,      // 重试延迟（毫秒）
    ],
];
