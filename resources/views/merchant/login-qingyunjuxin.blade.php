<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>青云聚信授权登录 - 雨骑士商家管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .animate-fade-in {
            animation: fadeIn 1s ease-in;
        }
        .animate-slide-up {
            animation: slideUp 0.8s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(30px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        .text-primary {
            color: #667eea;
        }
        .text-secondary {
            color: #764ba2;
        }
        .border-primary {
            border-color: #667eea;
        }
        .focus\:border-primary:focus {
            border-color: #667eea;
        }
        .focus\:ring-primary:focus {
            --tw-ring-color: rgba(102, 126, 234, 0.5);
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <div class="min-h-screen flex">
        <!-- 左侧背景区域 -->
        <div class="hidden lg:flex lg:w-1/2 gradient-bg relative overflow-hidden">
            <!-- 装饰性几何图形 -->
            <div class="absolute inset-0">
                <div class="absolute top-20 left-20 w-32 h-32 bg-white bg-opacity-10 rounded-full"></div>
                <div class="absolute top-40 right-32 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
                <div class="absolute bottom-32 left-32 w-40 h-40 bg-white bg-opacity-10 rounded-full"></div>
                <div class="absolute bottom-20 right-20 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
            </div>
            
            <!-- 左侧内容 -->
            <div class="relative z-10 flex flex-col justify-center px-12">
                <div class="max-w-md">
                    <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-20 mb-8 invert">
                    <h1 class="text-5xl font-bold text-white mb-6">雨骑士商家管理系统</h1>
                    <p class="text-white text-xl mb-6 opacity-90">青云聚信授权绑定</p>
                    <p class="text-white text-md mb-10 opacity-80">将您的青云聚信账号与雨骑士商家账号关联，享受便捷配送服务</p>
                </div>
            </div>
        </div>

        <!-- 右侧登录表单区域 -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8">
            <div class="w-full max-w-md">
                <!-- 移动端logo -->
                <div class="lg:hidden text-center mb-8">
                    <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-16 mx-auto mb-4">
                    <h1 class="text-2xl font-bold text-gray-800">雨骑士商家管理系统</h1>
                    <p class="text-gray-600 mt-2">青云聚信授权绑定</p>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-xl animate-slide-up">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">青云聚信授权登录</h2>
                    
                    <div class="mb-6">
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-700">
                                        请使用您的雨骑士商家账号登录，完成与青云聚信的授权绑定
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form action="{{ route('merchant.login.qingyunjuxin.post') }}" method="POST">
                        @csrf
                        <!-- 隐藏字段，传递青云聚信授权参数 -->
                        <input type="hidden" name="code" value="{{ $code }}">
                        <input type="hidden" name="redirect_uri" value="{{ $redirect_uri }}">
                        <input type="hidden" name="state" value="{{ $state }}">
                        <input type="hidden" name="source" value="{{ $source }}">
                        
                        <!-- 显示错误信息 -->
                        @if ($errors->any())
                            <div class="mb-6 bg-red-50 border-l-4 border-red-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        @foreach ($errors->all() as $error)
                                            <p class="text-sm text-red-700">{{ $error }}</p>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        <div class="mb-6">
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                            <input type="text" 
                                   id="phone" 
                                   name="phone" 
                                   value="{{ old('phone') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200 @error('phone') border-red-500 @enderror" 
                                   placeholder="请输入手机号" 
                                   required>
                            @error('phone')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200 @error('password') border-red-500 @enderror" 
                                   placeholder="请输入密码" 
                                   required>
                            @error('password')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <a href="{{ route('merchant.register') }}" class="text-sm text-primary hover:text-secondary transition-colors duration-200">还没有账号？立即注册</a>
                        </div>
                        
                        <button type="submit" class="w-full gradient-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-transform">授权绑定</button>
                    </form>
                </div>
                
                <!-- 底部信息 -->
                <div class="mt-8 text-center text-sm text-gray-500">
                    <p>© 2024 雨骑士. 保留所有权利.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
