<?php

namespace App\Services;

use App\Models\Merchant;
use App\Models\MerchantToken;
use App\Models\O2oErrandOrder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;

class QingyunJuxinService
{
    protected string $appKey;
    protected string $appSecret;
    protected string $tag;
    protected string $host;

    const ACCESS_TOKEN = "qyj_access_token";
    const REFRESH_TOKEN = "qyj_refresh_token";
    const TOKEN_EXPIRE = "qyj_expire_time";

    public function __construct(){
        $this->appKey = config('qingyunjuxin.app_key');
        $this->appSecret = config('qingyunjuxin.app_secret');
        $this->tag = "YQS";

        $environment = config('qingyunjuxin.environment', 'test');
        $baseUrls = config('qingyunjuxin.base_urls');

        if ($environment === 'production') {
            $this->host = $baseUrls['production'] . '/v1/delivery';
        } else {
            $this->host = $baseUrls['test'] . '/v1/delivery';
        }
    }

    /**
     * 获取访问令牌
     */
    public function accessToken($type, $code, $mobile, $storeId, $city, $cityCode)
    {
        Log::channel('qingyunjuxin')->info('青云聚信获取访问令牌', [
            'type' => $type,
            'code' => $code,
            'mobile' => $mobile,
            'store_id' => $storeId,
            'city' => $city,
            'city_code' => $cityCode
        ]);

        $result = $this->post("access_token", [
            "grant_type" => "$type",
            "code" => $code,
            "mobile" => $mobile,
            "store_id" => $storeId,
            "city" => $city,
            "city_code" => $cityCode
        ]);

        // 获取商家对应的用户ID
        $merchant = Merchant::find($type);
        $userId = $merchant ? $merchant->user_id : null;

        // 查询是否存在记录
        $tokenRecord = MerchantToken::where('merchant_id', $type)
            ->where('platform', O2oErrandOrder::APP_KEY_QYJ)
            ->when($userId, function($query) use ($userId) {
                return $query->where('user_id', $userId);
            })
            ->first();

        if ($tokenRecord) {
            // 更新现有记录
            $tokenRecord->update([
                'access_token' => $result['token'] ?? '',
                'refresh_token' => $result['refresh_token'] ?? '',
                'shop_id' => $result['shop_id'] ?? '',
                'expire_time' => $result['expire_time'] ?? 0,
                'refresh_expire_time' => $result['refresh_expire_time'] ?? 0,
            ]);
            Log::channel('qingyunjuxin')->info('青云聚信token记录已更新', ['merchant_id' => $type]);
        } else {
            // 创建新记录
            MerchantToken::create([
                'merchant_id' => $type,
                'user_id' => $userId,
                'platform' => O2oErrandOrder::APP_KEY_QYJ,
                'access_token' => $result['token'] ?? '',
                'refresh_token' => $result['refresh_token'] ?? '',
                'shop_id' => $result['shop_id'] ?? '',
                'expire_time' => $result['expire_time'] ?? 0,
                'refresh_expire_time' => $result['refresh_expire_time'] ?? 0,
            ]);
            Log::channel('qingyunjuxin')->info('青云聚信token记录已创建', ['merchant_id' => $type]);
        }

        // 缓存token信息
        if (isset($result['token'])) {
            Cache::put(self::ACCESS_TOKEN, $result['token'], now()->addHours(2));
        }
        if (isset($result['refresh_token'])) {
            Cache::put(self::REFRESH_TOKEN, $result['refresh_token'], now()->addDays(30));
        }
        if (isset($result['expire_time']) && $result['expire_time'] > 0) {
            Cache::put(self::TOKEN_EXPIRE, $result['expire_time'], now()->addDays(30));
        }

        return $result;
    }

    /**
     * 刷新访问令牌
     */
    public function refreshToken($refreshToken)
    {
        Log::channel('qingyunjuxin')->info('青云聚信刷新访问令牌');

        $result = $this->post("refresh_token", [
            "grant_type" => "refresh_token",
            "refresh_token" => $refreshToken
        ]);

        // 更新缓存
        if (isset($result['token'])) {
            Cache::put(self::ACCESS_TOKEN, $result['token'], now()->addHours(2));
        }
        if (isset($result['refresh_token'])) {
            Cache::put(self::REFRESH_TOKEN, $result['refresh_token'], now()->addDays(30));
        }

        return $result;
    }

    /**
     * 检查并刷新token
     */
    private function checkAndRefreshToken()
    {
        $expireTime = Cache::get(self::TOKEN_EXPIRE);
        if ($expireTime && $expireTime > 0) {
            $tt = intval($expireTime);
            // 即将过期（5分钟内）
            if($tt - time() <= 300) {
                Log::channel('qingyunjuxin')->info('青云聚信token即将过期，准备刷新');

                $accessToken = Cache::get(self::ACCESS_TOKEN);
                $refreshToken = Cache::get(self::REFRESH_TOKEN);
                if (empty($accessToken) || empty($refreshToken)){
                    // 尝试从数据库获取token
                    $tokenRecord = MerchantToken::where('platform', O2oErrandOrder::APP_KEY_QYJ)
                        ->whereNotNull('access_token')
                        ->whereNotNull('refresh_token')
                        ->first();

                    if ($tokenRecord) {
                        $accessToken = $tokenRecord->access_token;
                        $refreshToken = $tokenRecord->refresh_token;
                        Log::channel('qingyunjuxin')->info('从数据库获取token成功');
                    } else {
                        Log::channel('qingyunjuxin')->error('获取token失败：缓存和数据库中均无有效token');
                        throw new \Exception("获取token失败");
                    }
                }

                try {
                    $this->refreshToken($refreshToken);
                    Log::channel('qingyunjuxin')->info('青云聚信token刷新成功');
                } catch (\Exception $e) {
                    Log::channel('qingyunjuxin')->error('青云聚信token刷新失败', [
                        'message' => $e->getMessage()
                    ]);
                    throw $e;
                }
            }
        }
    }

    /**
     * 获取当前有效的访问令牌
     */
    private function getAccessToken()
    {
        $this->checkAndRefreshToken();

        $token = Cache::get(self::ACCESS_TOKEN);
        if (empty($token)) {
            // 尝试从数据库获取
            $tokenRecord = MerchantToken::where('platform', O2oErrandOrder::APP_KEY_QYJ)
                ->whereNotNull('access_token')
                ->first();

            if ($tokenRecord) {
                $token = $tokenRecord->access_token;
                Cache::put(self::ACCESS_TOKEN, $token, now()->addHours(2));
            }
        }

        return $token;
    }

    /**
     * 构建请求数据
     */
    private function getData(string $command, array $data = []): array
    {
        $timestamp = time();
        $requestData = [
            'app_key' => $this->appKey,
            'timestamp' => $timestamp,
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE)
        ];

        // 生成签名
        $requestData['sign'] = $this->generateSign($requestData);

        return $requestData;
    }

    /**
     * 生成签名
     */
    private function generateSign(array $data): string
    {
        // 排序参数
        ksort($data);

        // 构建签名字符串
        $signString = '';
        foreach ($data as $key => $value) {
            if ($key !== 'sign' && $value !== '') {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString .= 'app_secret=' . $this->appSecret;

        return md5($signString);
    }

    /**
     * POST请求
     */
    private function post(string $command, array $data = [], array $headers = [])
    {
        try {
            $headers['Content-Type'] = 'application/json;charset=utf-8';

            Log::channel('qingyunjuxin')->debug('青云聚信API请求', [
                'command' => $command,
                'data' => $data
            ]);

            $result = Curl::to($this->host . '/'. $command)
                ->asJson(true)
                ->withHeaders($headers)
                ->withData($this->getData($command, $data))
                ->post();

            Log::channel('qingyunjuxin')->debug('青云聚信API响应', [
                'command' => $command,
                'result' => $result
            ]);

            if(($result["code"]?? 0) == 200){
                return json_decode($result["data"], true);
            }

            Log::channel('qingyunjuxin')->error('青云聚信API错误', [
                'command' => $command,
                'code' => $result["code"] ?? 0,
                'message' => $result["msg"] ?? '未知错误'
            ]);

            throw new \Exception($result["msg"] ?? '青云聚信API调用失败');

        } catch (\Exception $e) {
            Log::channel('qingyunjuxin')->error('青云聚信API请求异常', [
                'command' => $command,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
